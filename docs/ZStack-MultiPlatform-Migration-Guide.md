# ZStack多平台支持迁移指南

## 概述

本指南详细说明如何从现有的 `ZStackClientWrapper` 迁移到新的 `ZStackClientWrapperMultiPlatform`，以解决多平台配置冲突问题。

## 问题背景

### 原有问题
1. **全局配置覆盖**：`ZSClient.configure()` 是全局静态配置，多平台调用会相互覆盖
2. **认证方式冲突**：不同平台的认证方式（session vs AccessKey）无法同时支持
3. **实例管理缺失**：缺乏独立的客户端实例管理机制
4. **配置竞争条件**：多线程环境下配置切换导致的竞争问题

### 解决方案
新的 `ZStackClientWrapperMultiPlatform` 提供：
- **独立实例管理**：每个平台维护独立的配置实例
- **配置隔离**：避免平台间配置相互干扰
- **线程安全**：平台级别的锁机制确保线程安全
- **智能切换**：只在需要时才重新配置ZSClient

## 核心架构改进

### 1. 平台实例管理器
```java
private static class PlatformClientInstance {
    private final String platformKey;      // 平台唯一标识
    private final String hostname;         // 主机名
    private final int port;               // 端口
    private final String contextPath;     // 上下文路径
    private final ReentrantLock instanceLock; // 平台专用锁
    private volatile boolean configured;   // 配置状态
    private volatile long lastConfigTime; // 最后配置时间
}
```

### 2. 智能配置切换
```java
private static void ensurePlatformConfigured(PlatformClientInstance instance) {
    // 只有当需要切换到不同平台时才重新配置
    boolean needReconfigure = false;
    
    if (currentActiveInstance == null || 
        !currentActiveInstance.getPlatformKey().equals(instance.getPlatformKey()) ||
        !instance.isConfigured()) {
        needReconfigure = true;
    }
    
    if (needReconfigure) {
        // 重新配置ZSClient
        ZSClient.configure(new ZSConfig.Builder()...);
        instance.setConfigured(true);
        currentActiveInstance = instance;
    }
}
```

## API迁移对照表

### 基础API迁移

| 原有方法 | 新方法 | 说明 |
|---------|--------|------|
| `executeWithClient()` | `executeWithMultiPlatformClient()` | 多平台安全执行 |
| `queryVmInstancesAsync()` | `queryVmInstancesMultiPlatform()` | 虚拟机查询 |
| `queryHostsAsync()` | `queryHostsMultiPlatform()` | 主机查询 |
| `queryZonesAsync()` | `queryZonesMultiPlatform()` | 区域查询 |
| `querySecurityGroupsAsync()` | `querySecurityGroupsMultiPlatform()` | 安全组查询 |
| `validateLogin()` | `validateLoginMultiPlatform()` | 登录验证 |

### 扩展API

| 新增方法 | 功能 |
|---------|------|
| `queryClustersMultiPlatform()` | 集群查询 |
| `queryVolumesMultiPlatform()` | 存储卷查询 |
| `queryPrimaryStoragesMultiPlatform()` | 主存储查询 |
| `queryImagesMultiPlatform()` | 镜像查询 |
| `queryL2NetworksMultiPlatform()` | L2网络查询 |
| `queryL3NetworksMultiPlatform()` | L3网络查询 |
| `getMetricDataMultiPlatform()` | 指标数据查询 |
| `queryZonesMultiPlatformWithRetry()` | 带重试的区域查询 |

## 迁移步骤

### 第一步：导入新类
```java
// 添加新的导入
import cn.coder.zj.module.collector.collect.token.zstack.ZStackClientWrapperMultiPlatform;
```

### 第二步：替换API调用

#### 示例1：基础查询迁移
```java
// 旧代码
QueryVmInstanceAction.Result vmResult = ZStackClientWrapper.queryVmInstancesAsync(platform);

// 新代码
QueryVmInstanceAction.Result vmResult = ZStackClientWrapperMultiPlatform.queryVmInstancesMultiPlatform(platform);
```

#### 示例2：异步执行迁移
```java
// 旧代码
taskExecutor.execute(() -> {
    try {
        QueryHostAction.Result result = ZStackClientWrapper.executeWithClientAsync(platform, () -> {
            QueryHostAction action = new QueryHostAction();
            ZStackClientWrapper.setAuthentication(action, platform);
            return action.call();
        });
        // 处理结果...
    } catch (Exception e) {
        log.error("查询失败", e);
    }
});

// 新代码
taskExecutor.execute(() -> {
    try {
        QueryHostAction.Result result = ZStackClientWrapperMultiPlatform.queryHostsMultiPlatform(platform);
        // 处理结果...
    } catch (Exception e) {
        log.error("查询失败", e);
    }
});
```

#### 示例3：多平台并发访问
```java
// 新代码：支持真正的多平台并发
CompletableFuture.allOf(
    CompletableFuture.runAsync(() -> {
        // 平台1的操作
        ZStackClientWrapperMultiPlatform.queryVmInstancesMultiPlatform(platform1);
    }),
    CompletableFuture.runAsync(() -> {
        // 平台2的操作（同时进行，不会冲突）
        ZStackClientWrapperMultiPlatform.queryHostsMultiPlatform(platform2);
    }),
    CompletableFuture.runAsync(() -> {
        // 平台3的操作（同时进行，不会冲突）
        ZStackClientWrapperMultiPlatform.queryZonesMultiPlatform(platform3);
    })
).join();
```

### 第三步：更新错误处理
```java
// 新的错误处理模式
try {
    QueryZoneAction.Result result = ZStackClientWrapperMultiPlatform.queryZonesMultiPlatformWithRetry(platform, 3);
    // 处理成功结果
} catch (RuntimeException e) {
    if (e.getMessage().contains("认证失败")) {
        // 处理认证错误
        log.error("平台认证失败: {}", platform.getPlatformName());
    } else if (e.getMessage().contains("网络")) {
        // 处理网络错误
        log.error("平台网络不可达: {}", platform.getPlatformName());
    } else {
        // 处理其他错误
        log.error("平台操作失败: {}", e.getMessage());
    }
}
```

## 管理和监控

### 实例状态监控
```java
// 获取平台实例统计信息
String stats = ZStackClientWrapperMultiPlatform.getPlatformInstanceStats();
log.info("平台实例状态:\n{}", stats);
```

### 实例清理管理
```java
// 清理特定平台实例
ZStackClientWrapperMultiPlatform.clearPlatformInstance(platform);

// 清理所有平台实例（通常在应用关闭时）
ZStackClientWrapperMultiPlatform.clearAllPlatformInstances();
```

## 性能优化

### 1. 配置缓存
- 每个平台的配置只在首次访问或切换时进行
- 避免重复的 `ZSClient.configure()` 调用
- 智能检测配置变更需求

### 2. 锁粒度优化
- 使用平台级别的锁，而非全局锁
- 不同平台可以并行执行，无需等待
- 最小化锁持有时间

### 3. 实例生命周期管理
- 自动创建和缓存平台实例
- 支持手动清理不需要的实例
- 内存使用优化

## 兼容性说明

### 向后兼容
- ✅ 原有的 `ZStackClientWrapper` 继续可用
- ✅ 可以逐步迁移，不需要一次性全部替换
- ✅ 认证方式完全兼容（sessionId 和 AccessKey）

### 新功能优势
- ✅ 真正的多平台并发支持
- ✅ 消除配置冲突问题
- ✅ 更好的错误处理和重试机制
- ✅ 详细的监控和管理功能

## 测试验证

### 单元测试
```bash
# 运行多平台支持测试
mvn test -Dtest=ZStackClientWrapperMultiPlatformTest
```

### 集成测试
```java
// 验证多平台并发访问
@Test
void testMultiPlatformConcurrentAccess() {
    // 同时访问3个不同平台
    CompletableFuture.allOf(
        CompletableFuture.runAsync(() -> testPlatform1Operations()),
        CompletableFuture.runAsync(() -> testPlatform2Operations()),
        CompletableFuture.runAsync(() -> testPlatform3Operations())
    ).join();
    
    // 验证所有操作都成功完成，没有配置冲突
}
```

## 最佳实践

### 1. 平台实例管理
- 在应用启动时预热常用平台实例
- 定期清理不再使用的平台实例
- 监控实例数量，避免内存泄漏

### 2. 错误处理
- 使用带重试的方法处理网络不稳定
- 区分认证错误和网络错误
- 实现适当的降级策略

### 3. 性能优化
- 批量操作时复用同一平台实例
- 避免频繁的平台切换
- 合理设置重试次数和间隔

## 总结

通过迁移到 `ZStackClientWrapperMultiPlatform`，您将获得：

1. **真正的多平台支持**：消除配置冲突，支持真正的并发访问
2. **更好的线程安全**：平台级别的锁机制，提高并发性能
3. **完善的错误处理**：更详细的错误信息和重试机制
4. **易于管理**：实例状态监控和清理功能
5. **向后兼容**：可以逐步迁移，不影响现有功能

这个解决方案彻底解决了ZStack多平台配置冲突的问题，为您的多平台环境提供了稳定可靠的支持。
