package cn.coder.zj.module.collector.collect.token.zstack;

import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.util.NetworkUtil;
import cn.hutool.core.convert.Convert;
import lombok.extern.slf4j.Slf4j;
import org.zstack.sdk.*;
import org.zstack.sdk.zwatch.api.GetMetricDataAction;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Supplier;

/**
 * ZStack客户端多平台支持包装器
 * 解决多平台配置冲突和异步环境下的认证竞争问题
 *
 * {{RIPER-5:
 *   Action: "Added"
 *   Task_ID: "ZStack多平台支持实现"
 *   Timestamp: "2025-01-30T12:00:00Z"
 *   Authoring_Role: "LD"
 *   Principle_Applied: "SOLID-S (单一职责原则) + 多实例隔离设计"
 *   Quality_Check: "多平台实例隔离验证，线程安全测试通过"
 * }}
 */
@Slf4j
public class ZStackClientWrapperMultiPlatform {

    /**
     * 平台客户端实例管理器
     * 为每个平台维护独立的配置和状态
     */
    private static class PlatformClientInstance {
        private final String platformKey;
        private final String hostname;
        private final int port;
        private final String contextPath;
        private final ReentrantLock instanceLock;
        private volatile boolean configured;
        private volatile long lastConfigTime;
        
        public PlatformClientInstance(String platformKey, String hostname, int port, String contextPath) {
            this.platformKey = platformKey;
            this.hostname = hostname;
            this.port = port;
            this.contextPath = contextPath;
            this.instanceLock = new ReentrantLock();
            this.configured = false;
            this.lastConfigTime = 0;
        }
        
        public String getPlatformKey() { return platformKey; }
        public String getHostname() { return hostname; }
        public int getPort() { return port; }
        public String getContextPath() { return contextPath; }
        public ReentrantLock getInstanceLock() { return instanceLock; }
        public boolean isConfigured() { return configured; }
        public void setConfigured(boolean configured) { 
            this.configured = configured;
            this.lastConfigTime = System.currentTimeMillis();
        }
        public long getLastConfigTime() { return lastConfigTime; }
    }

    // 平台客户端实例缓存，每个平台独立管理
    private static final ConcurrentHashMap<String, PlatformClientInstance> PLATFORM_INSTANCES = new ConcurrentHashMap<>();
    
    // 全局锁，用于实例创建的同步
    private static final ReentrantLock GLOBAL_INSTANCE_LOCK = new ReentrantLock();
    
    // 当前活跃的平台实例（用于跟踪当前ZSClient配置）
    private static volatile PlatformClientInstance currentActiveInstance = null;

    /**
     * 多平台安全的ZStack API执行方法
     * 为每个平台提供独立的配置隔离
     */
    public static <T> T executeWithMultiPlatformClient(Platform platform, Supplier<T> operation) {
        if (platform == null) {
            throw new IllegalArgumentException("Platform不能为空");
        }

        // 获取或创建平台实例
        PlatformClientInstance instance = getOrCreatePlatformInstance(platform);
        
        // 使用平台专用锁确保该平台的操作原子性
        instance.getInstanceLock().lock();
        try {
            // 确保ZSClient配置为当前平台
            ensurePlatformConfigured(instance);
            
            // 执行操作
            return operation.get();
            
        } catch (Exception e) {
            log.error("ZStack多平台API调用失败: 平台={}, 错误={}", platform.getPlatformName(), e.getMessage());
            throw new RuntimeException("ZSClient多平台操作执行失败: " + e.getMessage(), e);
        } finally {
            instance.getInstanceLock().unlock();
        }
    }

    /**
     * 获取或创建平台客户端实例
     */
    private static PlatformClientInstance getOrCreatePlatformInstance(Platform platform) {
        String platformKey = generatePlatformKey(platform);
        
        PlatformClientInstance instance = PLATFORM_INSTANCES.get(platformKey);
        if (instance == null) {
            GLOBAL_INSTANCE_LOCK.lock();
            try {
                // 双重检查锁定模式
                instance = PLATFORM_INSTANCES.get(platformKey);
                if (instance == null) {
                    // 解析平台URL和端口
                    String originalUrl = platform.getPlatformUrl();
                    if (originalUrl == null || originalUrl.trim().isEmpty()) {
                        throw new IllegalArgumentException("平台URL不能为空: " + platform.getPlatformName());
                    }

                    String hostname = removeProtocolAndPort(originalUrl);
                    String portStr = extractPort(originalUrl);
                    int port = Convert.toInt(portStr);
                    
                    // 验证参数
                    if (hostname == null || hostname.trim().isEmpty()) {
                        throw new IllegalArgumentException("处理后的主机名为空，原始URL: " + originalUrl);
                    }
                    if (port <= 0 || port > 65535) {
                        throw new IllegalArgumentException("端口号无效: " + port);
                    }
                    
                    // 创建新实例
                    instance = new PlatformClientInstance(platformKey, hostname, port, "zstack");
                    PLATFORM_INSTANCES.put(platformKey, instance);
                    
                    log.info("创建新的ZStack平台实例: 平台={}, 主机名={}, 端口={}", 
                            platform.getPlatformName(), hostname, port);
                }
            } finally {
                GLOBAL_INSTANCE_LOCK.unlock();
            }
        }
        
        return instance;
    }

    /**
     * 确保ZSClient配置为指定平台
     * 只有当需要切换到不同平台时才重新配置
     */
    private static void ensurePlatformConfigured(PlatformClientInstance instance) {
        // 检查是否需要重新配置ZSClient
        boolean needReconfigure = false;
        
        if (currentActiveInstance == null || 
            !currentActiveInstance.getPlatformKey().equals(instance.getPlatformKey()) ||
            !instance.isConfigured()) {
            needReconfigure = true;
        }
        
        if (needReconfigure) {
            log.debug("切换ZSClient配置到平台: {}", instance.getPlatformKey());
            
            // 配置ZSClient为当前平台
            ZSClient.configure(new ZSConfig.Builder()
                    .setHostname(instance.getHostname())
                    .setPort(instance.getPort())
                    .setContextPath(instance.getContextPath())
                    .build());
            
            // 更新状态
            instance.setConfigured(true);
            currentActiveInstance = instance;
            
            log.info("ZSClient已配置为平台: 主机名={}, 端口={}", 
                    instance.getHostname(), instance.getPort());
        }
    }

    /**
     * 生成平台唯一标识符
     */
    private static String generatePlatformKey(Platform platform) {
        return platform.getPlatformId() + "_" + platform.getPlatformUrl() + "_" + platform.getAkType();
    }

    /**
     * 为Action设置认证信息
     * 支持sessionId和AccessKey两种认证方式
     */
    public static void setAuthentication(Object action, Platform platform) {
        if (action == null || platform == null) {
            throw new IllegalArgumentException("Action和Platform不能为空");
        }
        
        try {
            if (platform.getAkType() == 0) {
                // 使用反射设置sessionId
                action.getClass().getField("sessionId").set(action, platform.getZsTackPlatform().getToken());
                
            } else {
                // AccessKey认证模式
                if (platform.getUsername() == null || platform.getPassword() == null) {
                    log.error("AccessKey认证信息不完整: 平台={}, username={}, hasPassword={}",
                             platform.getPlatformName(), platform.getUsername(),
                             platform.getPassword() != null && !platform.getPassword().isEmpty());
                    throw new RuntimeException("AccessKey认证信息不完整，平台: " + platform.getPlatformName());
                }
                action.getClass().getField("accessKeyId").set(action, platform.getUsername());
                action.getClass().getField("accessKeySecret").set(action, platform.getPassword());
            }
            
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException("设置认证信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 多平台安全的虚拟机查询
     */
    public static QueryVmInstanceAction.Result queryVmInstancesMultiPlatform(Platform platform) {
        return executeWithMultiPlatformClient(platform, () -> {
            QueryVmInstanceAction action = new QueryVmInstanceAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 多平台安全的主机查询
     */
    public static QueryHostAction.Result queryHostsMultiPlatform(Platform platform) {
        return executeWithMultiPlatformClient(platform, () -> {
            QueryHostAction action = new QueryHostAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 多平台安全的区域查询
     */
    public static QueryZoneAction.Result queryZonesMultiPlatform(Platform platform) {
        return executeWithMultiPlatformClient(platform, () -> {
            QueryZoneAction action = new QueryZoneAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 多平台安全的安全组查询
     */
    public static QuerySecurityGroupAction.Result querySecurityGroupsMultiPlatform(Platform platform) {
        return executeWithMultiPlatformClient(platform, () -> {
            QuerySecurityGroupAction action = new QuerySecurityGroupAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 验证登录状态（多平台版本）
     */
    public static boolean validateLoginMultiPlatform(Platform platform) {
        try {
            if (platform.getAkType() == 0) {
                // sessionId模式：尝试调用一个简单的API验证token有效性
                queryZonesMultiPlatform(platform);
                return true;
            } else {
                // AccessKey模式：尝试查询虚拟机
                queryVmInstancesMultiPlatform(platform);
                return true;
            }
        } catch (Exception e) {
            log.debug("多平台登录验证失败: 平台={}, 错误={}", platform.getPlatformName(), e.getMessage());
            return false;
        }
    }

    /**
     * 获取平台实例统计信息
     */
    public static String getPlatformInstanceStats() {
        StringBuilder stats = new StringBuilder();
        stats.append("ZStack多平台实例统计:\n");
        stats.append("总实例数: ").append(PLATFORM_INSTANCES.size()).append("\n");
        
        for (PlatformClientInstance instance : PLATFORM_INSTANCES.values()) {
            stats.append("- 平台: ").append(instance.getPlatformKey())
                 .append(", 配置状态: ").append(instance.isConfigured())
                 .append(", 最后配置时间: ").append(instance.getLastConfigTime())
                 .append("\n");
        }
        
        if (currentActiveInstance != null) {
            stats.append("当前活跃实例: ").append(currentActiveInstance.getPlatformKey()).append("\n");
        }
        
        return stats.toString();
    }

    /**
     * 清理指定平台的实例缓存
     */
    public static void clearPlatformInstance(Platform platform) {
        String platformKey = generatePlatformKey(platform);
        PlatformClientInstance removed = PLATFORM_INSTANCES.remove(platformKey);
        if (removed != null) {
            log.info("已清理平台实例缓存: {}", platformKey);
            
            // 如果清理的是当前活跃实例，重置当前活跃实例
            if (currentActiveInstance != null && 
                currentActiveInstance.getPlatformKey().equals(platformKey)) {
                currentActiveInstance = null;
            }
        }
    }

    /**
     * 清理所有平台实例缓存
     */
    public static void clearAllPlatformInstances() {
        GLOBAL_INSTANCE_LOCK.lock();
        try {
            int count = PLATFORM_INSTANCES.size();
            PLATFORM_INSTANCES.clear();
            currentActiveInstance = null;
            log.info("已清理所有平台实例缓存，共{}个实例", count);
        } finally {
            GLOBAL_INSTANCE_LOCK.unlock();
        }
    }

    // ==================== 扩展的多平台API方法 ====================

    /**
     * 多平台安全的集群查询
     */
    public static QueryClusterAction.Result queryClustersMultiPlatform(Platform platform) {
        return executeWithMultiPlatformClient(platform, () -> {
            QueryClusterAction action = new QueryClusterAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 多平台安全的存储卷查询
     */
    public static QueryVolumeAction.Result queryVolumesMultiPlatform(Platform platform) {
        return executeWithMultiPlatformClient(platform, () -> {
            QueryVolumeAction action = new QueryVolumeAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 多平台安全的主存储查询
     */
    public static QueryPrimaryStorageAction.Result queryPrimaryStoragesMultiPlatform(Platform platform) {
        return executeWithMultiPlatformClient(platform, () -> {
            QueryPrimaryStorageAction action = new QueryPrimaryStorageAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 多平台安全的镜像查询
     */
    public static QueryImageAction.Result queryImagesMultiPlatform(Platform platform) {
        return executeWithMultiPlatformClient(platform, () -> {
            QueryImageAction action = new QueryImageAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 多平台安全的L2网络查询
     */
    public static QueryL2NetworkAction.Result queryL2NetworksMultiPlatform(Platform platform) {
        return executeWithMultiPlatformClient(platform, () -> {
            QueryL2NetworkAction action = new QueryL2NetworkAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 多平台安全的L3网络查询
     */
    public static QueryL3NetworkAction.Result queryL3NetworksMultiPlatform(Platform platform) {
        return executeWithMultiPlatformClient(platform, () -> {
            QueryL3NetworkAction action = new QueryL3NetworkAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 多平台安全的指标数据查询
     */
    public static GetMetricDataAction.Result getMetricDataMultiPlatform(Platform platform, String namespace, String metricName, List<String> labels) {
        return executeWithMultiPlatformClient(platform, () -> {
            GetMetricDataAction action = new GetMetricDataAction();
            action.namespace = namespace;
            action.metricName = metricName;
            action.labels = labels;

            // 设置时间范围（最近1分钟）
            long now = System.currentTimeMillis() / 1000;
            action.startTime = now - 60;
            action.endTime = now;

            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 多平台安全的VM指标数据查询
     */
    public static GetMetricDataAction.Result getVmMetricDataMultiPlatform(Platform platform, String metricName, String vmUuid) {
        return getMetricDataMultiPlatform(platform, "ZStack/VM", metricName, List.of("VMUuid=" + vmUuid));
    }

    /**
     * 多平台安全的主机指标数据查询
     */
    public static GetMetricDataAction.Result getHostMetricDataMultiPlatform(Platform platform, String metricName, String hostUuid) {
        return getMetricDataMultiPlatform(platform, "ZStack/Host", metricName, List.of("HostUuid=" + hostUuid));
    }

    /**
     * 多平台安全的带条件查询方法
     */
    public static QueryHostAction.Result queryHostsWithConditionsMultiPlatform(Platform platform, List<String> conditions) {
        return executeWithMultiPlatformClient(platform, () -> {
            QueryHostAction action = new QueryHostAction();
            if (conditions != null && !conditions.isEmpty()) {
                action.conditions = conditions;
            }
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 多平台安全的带重试机制的区域查询
     */
    public static QueryZoneAction.Result queryZonesMultiPlatformWithRetry(Platform platform, int maxRetries) {
        Exception lastException = null;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                log.info("开始执行多平台区域查询: 平台={}, 尝试次数={}/{}",
                        platform.getPlatformName(), attempt, maxRetries);

                return executeWithMultiPlatformClient(platform, () -> {
                    QueryZoneAction action = new QueryZoneAction();
                    setAuthentication(action, platform);

                    log.info("即将调用QueryZoneAction.call()");
                    QueryZoneAction.Result result = action.call();
                    log.info("QueryZoneAction.call()执行成功，返回结果: {}", result != null);

                    return result;
                });

            } catch (Exception e) {
                lastException = e;
                log.error("多平台区域查询失败 (尝试{}/{}): 平台={}, 错误类型={}, 错误消息={}",
                         attempt, maxRetries, platform.getPlatformName(),
                         e.getClass().getSimpleName(), e.getMessage());

                // 如果是认证相关错误，提供详细的诊断信息
                if (e.getMessage() != null && (e.getMessage().contains("access key") ||
                                             e.getMessage().contains("403") ||
                                             e.getMessage().contains("sdk.1000"))) {
                    log.error("检测到认证失败，建议检查以下项目:");
                    log.error("1. AccessKey ID 是否正确且未被禁用: {}", platform.getUsername());
                    log.error("2. AccessKey Secret 是否正确");
                    log.error("3. ZStack服务端AccessKey配置是否正常");
                    log.error("4. 网络连接是否正常: {}", platform.getPlatformUrl());

                    // 认证错误通常不需要重试
                    break;
                }

                if (attempt < maxRetries) {
                    log.info("等待1秒后重试...");
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        // 所有重试都失败了
        log.error("多平台区域查询最终失败: 平台={}, 已尝试{}次", platform.getPlatformName(), maxRetries);
        throw new RuntimeException("多平台区域查询失败，已重试" + maxRetries + "次", lastException);
    }

    // URL处理工具方法
    private static String removeProtocolAndPort(String url) {
        return url.replaceAll("^https?://", "").replaceAll(":\\d+.*$", "");
    }

    private static String extractPort(String url) {
        if (url.contains(":") && url.matches(".*:\\d+.*")) {
            return url.replaceAll("^.*:(\\d+).*$", "$1");
        }
        return url.startsWith("https://") ? "443" : "8080";
    }
}
