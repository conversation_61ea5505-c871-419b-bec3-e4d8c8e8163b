package cn.coder.zj.module.collector.collect.token.zstack;

import cn.coder.zj.module.collector.collect.token.AbstractToken;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.dal.platform.common.MonitorInfo;
import cn.coder.zj.module.collector.dal.platform.zstack.ZsTackPlatform;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.NetworkUtil;
import cn.coder.zj.module.collector.util.Sha256;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.convert.Convert;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.AsyncTaskExecutor;
import org.zstack.sdk.*;

import java.util.*;
import java.util.concurrent.FutureTask;
import java.util.concurrent.TimeUnit;

import static cn.coder.zj.module.collector.enums.PlatformType.ZS_TACK;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.coder.zj.module.collector.util.PortUtil.extractPort;
import static cn.coder.zj.module.collector.util.PortUtil.removeProtocolAndPort;


@Slf4j
public class ZsTackTokenImpl extends AbstractToken {


    @Override
    public void preCheck(Platform platform) {
        AsyncTaskExecutor asyncTaskExecutor = SpringBeanUtils.getBean(AsyncTaskExecutor.class);
        final int MAX_ATTEMPTS = 3;
        int successCount = 0;
        int failureCount = 0;

        log.info("开始对平台 {} 进行连接检查，将尝试 {} 次", platform.getPlatformName(), MAX_ATTEMPTS);


        for (int attempt = 1; attempt <= MAX_ATTEMPTS; attempt++) {
            int finalAttempt = attempt;
            FutureTask<Boolean> task = new FutureTask<>(() -> {
                // 使用synchronized确保ZSClient配置和调用的原子性，避免多线程竞争
                synchronized (ZsTackTokenImpl.class) {
                    if (platform.getAkType() == 0) {
                        try {

                            LogInByAccountAction.Result res =  ZStackClientWrapper.loginByAccount(platform);

                            // 4. 严格验证返回结果的每个层级
                            if (res == null) {
                                log.debug("平台 {} 第{}次尝试: 登录响应为null", platform.getPlatformName(), finalAttempt);
                                return false;
                            }

                            if (res.value == null) {
                                log.debug("平台 {} 第{}次尝试: 登录结果值为null", platform.getPlatformName(), finalAttempt);
                                return false;
                            }

                            LogInResult loginResult = res.value;
                            if (loginResult.getInventory() == null) {
                                log.debug("平台 {} 第{}次尝试: 登录清单为null", platform.getPlatformName(), finalAttempt);
                                return false;
                            }

                            // 5. 验证token有效性
                            String token = loginResult.getInventory().uuid;
                            if (token == null || token.trim().isEmpty()) {
                                log.debug("平台 {} 第{}次尝试: 获取的token为空", platform.getPlatformName(), finalAttempt);
                                return false;
                            }

                            log.debug("平台 {} 第{}次尝试验证通过，token: {}",
                                    platform.getPlatformName(), finalAttempt, token.substring(0, Math.min(8, token.length())) + "...");
                            return true;

                        } catch (Exception e) {
                            log.debug("平台 {} 第{}次尝试异常: {}", platform.getPlatformName(), finalAttempt, e.getMessage());
                            return false;
                        }
                    } else {
                        List<MonitorInfo> vmUuids = getUuids(platform, "VM");
                        if (vmUuids.size() > 0) {
                            return true;
                        }
                        return false;
                    }

                }
            });

            try {
                asyncTaskExecutor.submit(task);
                boolean isConnected = task.get(10, TimeUnit.SECONDS);

                if (isConnected) {
                    successCount++;
                    log.info("平台 {} 第{}次连接检查成功", platform.getPlatformName(), attempt);
                } else {
                    failureCount++;
                    log.warn("平台 {} 第{}次连接检查失败", platform.getPlatformName(), attempt);
                }

            } catch (Exception e) {
                failureCount++;
                log.error("平台 {} 第{}次连接检查超时", platform.getPlatformName(), attempt);
                task.cancel(true);
            }

            // 继续所有尝试，不提前退出
            if (attempt < MAX_ATTEMPTS) {
                try {
                    // 两次尝试之间等待一小段时间，确保在一分钟内完成3次尝试
                    Thread.sleep(15000); // 等待15秒
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        // 根据最终结果判断平台状态
        boolean finalResult = successCount > 0; // 只要有一次成功就认为平台可用

        if (finalResult) {
            log.info("平台 {} 连接检查完成: 成功 {}/{} 次，判定为在线",
                    platform.getPlatformName(), successCount, MAX_ATTEMPTS);
        } else {
            log.error("平台 {} 连接检查完成: 失败 {}/{} 次，判定为离线",
                    platform.getPlatformName(), failureCount, MAX_ATTEMPTS);
        }

        // 在所有尝试结束后更新平台状态
        updatePlatformStatus(platform, finalResult);
    }


    @Override
    public synchronized void token(Platform platform) {
        try {
            String processedUrl = removeProtocolAndPort(platform.getPlatformUrl());
            String port = extractPort(platform.getPlatformUrl());
            if (platform.getAkType() == 0) {
                LogInByAccountAction.Result res =  ZStackClientWrapper.loginByAccount(platform);

                try {
                    if (res == null || res.value == null || res.value.getInventory() == null) {
                        log.error("平台 {} 登录失败: 返回结果为空", platform.getPlatformName());
                        updatePlatformStatus(platform, false);
                        return;
                    }
                    String token = res.value.getInventory().uuid;
                    platform.setZsTackPlatform(ZsTackPlatform.builder().token(token).build());
                    List<MonitorInfo> vmUuids = getUuids(platform, "VM");
                    List<MonitorInfo> hostUuids = getUuids(platform, "HOST");
                    platform.setZsTackPlatform(ZsTackPlatform.builder().token(token).vmUuids(vmUuids).hostUuids(hostUuids).type(platform.getAkType()).build());

                    Map<String, Object> map = new HashMap<>();
                    map.put(platform.getPlatformId().toString(), platform);
                    CacheService.put(ZS_TACK.code(), map);

                } catch (Exception e) {

                }
            } else {
                ZSClient.configure(new ZSConfig.Builder().setHostname(processedUrl).setPort(Convert.toInt(port)).setContextPath("zstack").build());
                List<MonitorInfo> vmUuids = getUuids(platform, "VM");
                List<MonitorInfo> hostUuids = getUuids(platform, "HOST");
                platform.setZsTackPlatform(ZsTackPlatform.builder().token("").vmUuids(vmUuids).hostUuids(hostUuids).type(platform.getAkType()).build());
                Map<String, Object> map = new HashMap<>();
                map.put(platform.getPlatformId().toString(), platform);
                CacheService.put(ZS_TACK.code(), map);
            }

        } catch (Exception e) {

        }
    }

    /**
     * 发送平台异常信息
     */
    private void updatePlatformStatus(Platform platform, boolean isOnline) {
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        if (isOnline) {
            platform.setState(0L);
        } else {
            platform.setState(1L);
        }
        platform.setDateTime(new Date());
        sendMessageService.sendMessage(CacheService.getCtx("ctx"), ClusterMsg.Message.newBuilder().setData(GsonUtil.GSON.toJson(platform)).setType(ClusterMsg.MessageType.DETECT).setTime(System.currentTimeMillis()).build());
    }

    public List<MonitorInfo> getUuids(Platform platform, String actionType) {
        List<MonitorInfo> uuidList = new ArrayList<>();
        List<?> inventories;
        if ("VM".equals(actionType)) {
            QueryVmInstanceAction.Result result = ZStackClientWrapper.queryVmInstancesAsync(platform);

            inventories = result.value.inventories;
        } else if ("HOST".equals(actionType)) {
            QueryHostAction.Result result = ZStackClientWrapper.queryHostsAsync(platform);
            inventories = result.value.inventories;
        } else {
            throw new IllegalArgumentException("Invalid action type: " + actionType);
        }
        for (Object o : inventories) {
            JsonObject jsonObject = GSON.toJsonTree(o).getAsJsonObject();
            JsonElement hypervisorType = jsonObject.get("hypervisorType");
            JsonElement uuid = jsonObject.get("uuid");
            JsonElement name = jsonObject.get("name");
            if (hypervisorType != null && !hypervisorType.isJsonNull() && uuid != null && !uuid.isJsonNull() && "KVM".equals(hypervisorType.getAsString())) {
                uuidList.add(MonitorInfo.builder()
                        .name(name.getAsString())
                        .uuid(uuid.getAsString())
                        .build());
            }
        }
        return uuidList;
    }


    @Override
    public String supportProtocol() {
        return ZS_TACK.code();
    }


}
